{"expo": {"scheme": "myapp", "name": "lesson2", "slug": "lesson2", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "runtimeVersion": {"policy": "appVersion"}, "bundleIdentifier": "com.yyaprak.lesson2", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.yyaprak.lesson2"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "acb57452-fbf4-4431-ae16-c7ea20cf146c"}}, "owner": "<PERSON><PERSON><PERSON><PERSON>", "updates": {"url": "https://u.expo.dev/acb57452-fbf4-4431-ae16-c7ea20cf146c"}, "plugins": ["expo-router", ["react-native-ble-manager", {"isBleRequired": true, "neverForLocation": true, "companionDeviceEnabled": false, "bluetoothAlwaysPermission": "Allow BLE DEMO APP to connect to bluetooth devices"}], "react-native-ble-plx"]}}