{"name": "lesson2", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"eas-cli": "^16.14.1", "expo": "~53.0.17", "expo-constants": "~17.1.7", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.16", "react": "19.0.0", "react-native": "0.79.5", "react-native-appwrite": "^0.10.0", "react-native-ble-manager": "^12.1.5", "react-native-ble-plx": "^3.5.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "expo-dev-client": "~5.2.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10"}, "private": true}